#!/usr/bin/env python3
"""
Test script for the new content category system.
Tests the digital twin's ability to handle different content categories and generate appropriate follow-up questions.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from core.conversational_engine import ConversationalEngine
from core.models import ContentCategoryType

def test_content_categories():
    """Test the content category system with various questions."""
    
    # Rickshaw Coffee bot ID
    bot_id = "1cf7f2c9-f132-40e2-922f-634dfafa9605"
    engine = ConversationalEngine(bot_id)
    
    test_cases = [
        {
            "question": "Tell me about your daily food menu",
            "expected_category": "daily_food_menu",
            "description": "Testing menu content selection"
        },
        {
            "question": "What coffee products do you offer?",
            "expected_category": "products", 
            "description": "Testing product content selection"
        },
        {
            "question": "Do you provide catering services?",
            "expected_category": "catering",
            "description": "Testing catering content selection"
        },
        {
            "question": "Share a personal story with me",
            "expected_category": "stories",
            "description": "Testing story content selection"
        },
        {
            "question": "What's your background?",
            "expected_category": "any",
            "description": "Testing general question handling"
        }
    ]
    
    print("🧪 Testing Content Category System")
    print("=" * 50)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{i}. {test_case['description']}")
        print(f"Question: '{test_case['question']}'")
        
        try:
            response = engine.generate_response(test_case['question'], bot_id)
            
            print(f"✅ Response generated successfully")
            print(f"Response preview: {response.response[:100]}...")
            
            print(f"Follow-up questions:")
            for j, question in enumerate(response.follow_up_questions, 1):
                print(f"  {j}. {question}")
            
            # Verify follow-up question structure
            if len(response.follow_up_questions) == 3:
                print("✅ Correct number of follow-up questions (3)")
            else:
                print(f"❌ Expected 3 follow-up questions, got {len(response.follow_up_questions)}")
            
            # Check if questions are diverse (not all the same)
            unique_questions = set(response.follow_up_questions)
            if len(unique_questions) >= 2:
                print("✅ Follow-up questions are diverse")
            else:
                print("⚠️  Follow-up questions may be too similar")
                
        except Exception as e:
            print(f"❌ Error: {e}")
            import traceback
            traceback.print_exc()
    
    print("\n" + "=" * 50)
    print("🎉 Content Category System Test Complete!")

def test_follow_up_question_structure():
    """Test that follow-up questions follow the new structure."""
    
    print("\n🔍 Testing Follow-up Question Structure")
    print("=" * 50)
    
    bot_id = "1cf7f2c9-f132-40e2-922f-634dfafa9605"
    engine = ConversationalEngine(bot_id)
    
    # Test with a menu question to see if we get questions about other categories
    response = engine.generate_response("Tell me about your breakfast options", bot_id)
    
    print("Question: 'Tell me about your breakfast options'")
    print(f"Response: {response.response[:100]}...")
    print("\nFollow-up questions analysis:")
    
    for i, question in enumerate(response.follow_up_questions, 1):
        print(f"  {i}. {question}")
        
        # Analyze question content to guess category focus
        question_lower = question.lower()
        if any(word in question_lower for word in ['menu', 'food', 'dish', 'eat', 'breakfast', 'lunch']):
            category_guess = "menu/food"
        elif any(word in question_lower for word in ['coffee', 'product', 'drip', 'cold brew']):
            category_guess = "products"
        elif any(word in question_lower for word in ['cater', 'event', 'service']):
            category_guess = "catering"
        elif any(word in question_lower for word in ['story', 'experience', 'memory', 'feel']):
            category_guess = "stories"
        elif 'promotion' in question_lower:
            category_guess = "call-to-action"
        else:
            category_guess = "general"
            
        print(f"     → Likely category focus: {category_guess}")
    
    print("\n✅ Follow-up question structure analysis complete")

if __name__ == "__main__":
    test_content_categories()
    test_follow_up_question_structure()
