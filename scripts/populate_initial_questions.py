#!/usr/bin/env python3
"""
Scrip<PERSON> to populate initial questions for digital twins.
This script adds the predefined initial questions for each content category.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.supabase_client import supabase_client
from core.models import InitialQuestion, ContentCategoryType
from uuid import UUID

# Bot IDs (from the memories)
RICKSHAW_COFFEE_BOT_ID = "1cf7f2c9-f132-40e2-922f-634dfafa9605"
METRO_FARM_BOT_ID = "fb194cb4-2c57-42bf-b080-c9389065be02"

def populate_initial_questions():
    """Populate initial questions for the Rickshaw Coffee bot."""
    
    # Initial questions for each category
    initial_questions_data = [
        # Brand Story / Stories questions
        {
            "category_type": ContentCategoryType.STORIES,
            "questions": [
                "How can you \"slow down to go fast\" with a cup of coffee?",
                "A cop's coffee brand, inspired by an ex-convict?",
                "What do rickshaws have to do with heritage coffee?"
            ]
        },
        # Daily Food Menu questions
        {
            "category_type": ContentCategoryType.DAILY_FOOD_MENU,
            "questions": [
                "A noodle dish that can only be found in Singapore?",
                "Which local breakfast was first adapted from British jam by Hainanese cooks?",
                "Why does this menu pair Italian pasta with local sambal?"
            ]
        },
        # Products questions
        {
            "category_type": ContentCategoryType.PRODUCTS,
            "questions": [
                "What does it mean for a cold brew to have a \"placemaking ethos\"?",
                "What is the purpose of steeping the cold brew for exactly 18 hours?",
                "What makes the beans used for the cold brew \"ethically sourced\"?"
            ]
        }
    ]
    
    try:
        print("Populating initial questions for Rickshaw Coffee bot...")
        
        for category_data in initial_questions_data:
            category_type = category_data["category_type"]
            questions = category_data["questions"]
            
            print(f"\nAdding {category_type.value} questions:")
            
            for question_text in questions:
                initial_question = InitialQuestion(
                    bot_id=UUID(RICKSHAW_COFFEE_BOT_ID),
                    category_type=category_type,
                    question=question_text
                )
                
                supabase_client.insert_initial_question(initial_question)
                print(f"  ✓ {question_text}")
        
        print("\n✅ Initial questions populated successfully!")
        
        # Verify the questions were inserted
        print("\n🔍 Verifying inserted questions...")
        grouped_questions = supabase_client.get_initial_questions_by_bot(RICKSHAW_COFFEE_BOT_ID)
        
        for category_type, questions in grouped_questions.items():
            print(f"\n{category_type.value.upper()} ({len(questions)} questions):")
            for question in questions:
                print(f"  - {question.question}")
        
    except Exception as e:
        print(f"❌ Error populating initial questions: {e}")
        raise

def populate_metro_farm_questions():
    """Populate initial questions for the Metro Farm bot (example for extensibility)."""
    
    # Example questions for Metro Farm - these would be customized for their content
    initial_questions_data = [
        # Stories questions for Metro Farm
        {
            "category_type": ContentCategoryType.STORIES,
            "questions": [
                "How did urban farming change your perspective on food?",
                "What inspired you to start growing food in the city?",
                "How do you connect communities through farming?"
            ]
        },
        # You could add more categories as needed for Metro Farm
    ]
    
    try:
        print("Populating initial questions for Metro Farm bot...")
        
        for category_data in initial_questions_data:
            category_type = category_data["category_type"]
            questions = category_data["questions"]
            
            print(f"\nAdding {category_type.value} questions:")
            
            for question_text in questions:
                initial_question = InitialQuestion(
                    bot_id=UUID(METRO_FARM_BOT_ID),
                    category_type=category_type,
                    question=question_text
                )
                
                supabase_client.insert_initial_question(initial_question)
                print(f"  ✓ {question_text}")
        
        print("\n✅ Metro Farm initial questions populated successfully!")
        
    except Exception as e:
        print(f"❌ Error populating Metro Farm initial questions: {e}")
        raise

if __name__ == "__main__":
    # Populate questions for Rickshaw Coffee
    populate_initial_questions()
    
    # Uncomment to populate questions for Metro Farm as well
    # print("\n" + "="*50)
    # populate_metro_farm_questions()
