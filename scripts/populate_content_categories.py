#!/usr/bin/env python3
"""
Script to populate content categories with the provided content.
This script adds the Daily Food Menu, Products, and Catering content to the database.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.supabase_client import supabase_client
from core.models import ContentCategory, ContentCategoryType
from uuid import UUID

# Bot IDs (from the memories)
RICKSHAW_COFFEE_BOT_ID = "1cf7f2c9-f132-40e2-922f-634dfafa9605"
METRO_FARM_BOT_ID = "fb194cb4-2c57-42bf-b080-c9389065be02"

# Content for Daily Food Menu
DAILY_FOOD_MENU_CONTENT = """# 🍴 *Our Menu, Our Story*

A love letter to Singapore's kopitiam soul and Southeast Asian food heritage

At our table, food isn't just about eating—it's about remembering, reconnecting, and reliving. Each dish we serve carries with it the rhythm of hawker centres, the warmth of grandma's kitchen, and the sounds of kopi uncles calling out orders in shorthand known only to locals.

This menu is our tribute to the flavours that built Singapore—and to the many hands, cultures, and histories that shaped them.

---

## ☀️ *Breakfast Sets – Morning Rituals*

*Kaya Toast Set*
A Straits-born invention, kaya toast brings together Hainanese ingenuity and British influence. Hainanese cooks working for colonial families adapted jam-making into a coconut-pandan custard spread we now call kaya. Paired with charred toast and kopi, this breakfast is as Singaporean as it gets.

*Butter Sugar Toast Set*
A simpler joy—toast slathered in cold butter and sprinkled with sugar. Once an affordable Western-style treat, it became a staple in kopi tiams, especially for the morning crowd.

*Sandwich Set*
Local sandwiches evolved post-war—filled with egg mayo, sardine, ham or luncheon meat. Perfectly designed for busy mornings, they bridge kopitiam nostalgia with contemporary tastes.

*Chan Dan Mee*
This is our own homegrown breakfast noodle creation—"chan dan" meaning fried egg, paired with mee for a full, hearty morning option. Think of it as dry mee pok meets homestyle Hakka flavour.

*Bee Hoon Sets (1 & 2)*
Nothing says Singapore breakfast like bee hoon. This humble rice vermicelli dish hails from Teochew roots, but it's the post-war pairing with luncheon meat, hot dog and egg that made it iconic. A tribute to 1950s economic survival, now a timeless comfort.

---

## 🧾 *Kopi & Teh – The Language of the Everyday*

We don't just serve drinks—we serve heritage. Kopi and Teh in Singapore are more than beverages. They are a language, a ritual, a culture of their own.

* *Kopi* = Coffee with condensed milk
* *Teh* = Tea with condensed milk
* *C* = With evaporated milk
* *O* = No milk
* *Peng* = Iced
* *Gao* = Strong
* *Poh* = Weak
* *Siew Dai* = Less sugar
* *Kah Dai* = More sugar
* *Kosong* = No sugar
* *Di Lo* = Extra concentrated, undiluted

And then there's *Yuan Yang*—a smooth fusion of coffee and tea, born in Hong Kong and localised here with kopi punch.

This is our shorthand, our art form, our community connection.

---

## 🍛 *Lunch Sets – The Midday Table*

From regional classics to remixed heritage dishes, our lunch options reflect the full canvas of Singapore's multicultural dining legacy.

### 🌾 *Local & Regional Staples*

* *Hainanese Chicken Rice*: Poached chicken, garlic rice, chilli—Singapore's "national dish", born from Hainanese migrants.
* *Laksa*: A fiery, coconut-rich noodle soup from Peranakan kitchens, with roots in Straits Chinese culture.
* *Nasi Lemak*: Malay coconut rice paired with sambal and fried treats—once a farmer's fuel, now a comfort classic.
* *Mee Siam*: Rice vermicelli in sour-spicy gravy, echoing Malay and Peranakan harmony.
* *Prawn Mee / Fishball Mee / Lor Mee / Mee Soto*: Each dish a story from a different dialect group—Teochew, Hokkien, Malay—all simmered into Singapore's hawker soul.

### 🍜 *Tze Char Meets Fusion*

* *Mui Fun / Hor Fun / Black Pepper Beef*: Cantonese wok hei classics made for sharing.
* *Tau Kwa Mince Chicken*: A Hokkien-style stir-fry marrying tofu and savoury meat—simple, satisfying.

### 🇯🇵 *Japanese-Inspired Comforts*

* *Japanese Curry Rice / Katsudon*: Sweet and earthy curry, pork or chicken cutlets, a comfort food favourite with roots in Japan's Meiji era and Singapore's food courts.
* *Aglio & Vongole Pasta*: Italian bases reimagined with subtle local flair. Aglio sometimes meets sambal. Vongole stays true to its coastal Mediterranean soul.

### 🍗 *Modern Wok Westerns*

* *Sweet & Sour Chicken / Lemon Chicken / Sesame Chicken / Thai Sweet Chilli Chicken*: Born from Chinese diaspora kitchens in North America and Southeast Asia—classic "zhap cai png" mixed rice fare.
* *Ginger Chicken / Chicken Chop / Teriyaki Chicken / Hainanese Chicken Chop*: Western food meets Asian sauces, served up Tze Char-style.

### 🧂 *Southeast Asian Heat & Umami*

* *Tom Yum Soup / Thai Basil / Thai Sweet Chilli Rice*: Thai street favourites that made their way into our hawker centres.
* *Salted Egg Chicken / Satay Bee Hoon*: Hyperlocal hybrids. Satay Bee Hoon is a true Singapore original, not found anywhere else in the region.

---

## 🚛 *Special Mentions*

*Rickshaw Noodles*
Named after the rickshaw pullers of old Singapore, this noodle dish evokes the streets of Chinatown and the resilience of the working class. A nostalgic bowl reimagined.

*3 Colour Rice*
A contemporary mixed plate inspired by nasi campur—three proteins or toppings arranged bento-style. Designed for visual punch and flavour balance.

*Happy Cup, Coldbrew, Matcha Latte*
These newer drinks reflect our evolution. From heritage to modern café culture, our beverage line-up spans the past, present, and future.

---

## 🌱 *Why It Matters*

This menu is more than food—it's our *living archive, a **storytelling platform, and a way to **honour traditions while embracing change*. Each item you see here isn't just delicious. It's a page from Singapore's culinary storybook, lovingly served on your tray."""

DAILY_FOOD_MENU_SUMMARY = "Complete daily food menu featuring Singapore's kopitiam heritage, from breakfast sets and traditional kopi culture to lunch dishes representing multicultural dining legacy."

# Content for Products
PRODUCTS_CONTENT = """☕ Rickshaw Coffee Drip Bag

Heritage. Anywhere.

Brief Description:
Brew a piece of Singapore, wherever you are.
The Rickshaw Coffee Drip Bag captures the bold, nostalgic flavour of kopitiam-style coffee in a travel-ready, tear-and-pour format. Roasted locally and crafted for the everyday wanderer, it's a tribute to the rickshaw pullers who built our streets and the kopitiam uncles who fuel our mornings.

This isn't just a coffee—it's heritage in your hands. A quiet protest against generic café chains. A small ritual that roots you in place.

Key Info:

	•	Roast Level: Medium-dark
	•	Serving Size: 10g / cup (best with 180–200ml hot water)
	•	Format: Single-serve drip bag with built-in paper wings
	•	Packaging: Sealed for freshness best consumed within 6 months 
	•	Made in: Singapore – roasted & packed locally

Ideal For:
	•	Office breaks, hotel stays, gifting to overseas friends, grounding moments

⸻

❄️ Rickshaw Coffee Cold Brew

Brief Description:
Brewed slow, with soul.
Rickshaw Coffee Cold Brew is a smooth, low-acid coffee steeped for 18 hours, balancing robustness and clarity in every sip. It's made with ethically sourced beans roasted in Singapore, grounded in our placemaking ethos and served chilled — just like the rhythm of hawker mornings.

Cold Brew is our ode to a city in motion: fast on the outside, calm at the core. Drink it to remember where you're from, or to find peace where you are.

Key Info:
	•	Steep Time: 18 hours, cold-brewed for low acidity
	•	Ready-to-Drink: 250ml / 500ml bottles (custom sizes available)
	•	Shelf Life: 14 days refrigerated
	•	No Sugar, No Preservatives
	•	Made in: Singapore

Serving Suggestions:
	•	Straight up or over ice
	•	Add milk or oat milk for a creamy twist
	•	Pair with kaya toast, curry puffs, or burnt cheesecake

⸻

🌱 Brand Ethos Behind Rickshaw Coffee

At Rickshaw, we honour the quiet resilience of the everyman. Our brand celebrates Singapore's vanishing trades, kopitiam culture, and the simple joy of everyday rituals. We roast locally, tell stories boldly, and brew with the belief that coffee can ground people, places, and purpose.

We don't just sell coffee—we serve community in a cup. #notjustcoffee #community #madeinsingapore"""

PRODUCTS_SUMMARY = "Rickshaw Coffee products including drip bags and cold brew, celebrating Singapore's kopitiam heritage and local coffee culture."

# Content for Catering
CATERING_CONTENT = """The Canteen is a space for the Sherwood community to meet one another and connect over traditional heritage food. Creating a space for the community to unwind and take it slow to appreciate the greenery on the hill. Each meal becomes a shared moment, where colleagues gather and bonds are strengthened over simple conversations.
– Benjamin Tan

---

## BENTO SET – \\$10.00 per pax (No GST)

Minimum 20 sets per order

*BASIC (Choice of one)*

* Jasmine Rice
* Garlic Fried Rice

*PROTEIN (Choice of one)*

* Katsu Don
* Fried Ebi
* Teriyaki Chicken
* 2 x XL Satay
* Thai Sweet Chilli Fish

*SIDES*

* Seasonal Mix Vegetables (Locally farmed, no pesticides)
* Sambal Kang Kong
* Long Bean with Oyster Sauce
* Mix Salad (Locally farmed, no pesticides)
* Mango Salad

*DRINKS (Choice of one)*

* Coffee
* Tea

---

## TEABREAK A – \\$5.00 per pax (No GST)

Minimum 30 pax

*MINI SANDWICH (Choice of two)*

* Tuna Sandwich
* Egg Mayo

*MINI KUEH (Choice of two)*

* DarDar
* Rainbow Lapis
* Bingka
* Ang Ku Kueh
* Kueh Silat

*Additional Kueh*: +\\$1 per pax for mix platter

*DRINKS*

* Coffee & Tea with milk and sugar

---

## TEABREAK B – \\$5.00 per pax (No GST)

Same as Teabreak A
Minimum 30 pax

*MINI SANDWICH (Choice of two)*

* Tuna Sandwich
* Egg Mayo

*MINI KUEH (Choice of two)*

* DarDar
* Rainbow Lapis
* Bingka
* Ang Ku Kueh
* Kueh Silat

*Additional Kueh*: +\\$1 per pax for mix platter

*DRINKS*

* Coffee & Tea with milk and sugar

---

## TEABREAK C – \\$9.00 per pax (No GST)

Minimum 30 pax

*BEE HOON (Choice of one)*

* Fried Beehoon
* Fried Kway Teow

*LOCAL FAVOURITES (Choice of two)*

* Spring Rolls
* Samosas
* Curry Puff Potato (Vegetarian)

*SKEWERS (100 sticks)*

* XL Chicken Satay
* Rainbow Skewers (Vegetarian)

*DRINK*

* Ice Lemon Tea

---

## DESSERT & COLDBREW

### BO BO CHOW CHOW – \\$3.00 per tub (No GST)

Minimum 30 pax
Handmade taro, Japanese sweet potato balls with Azuki red bean and grass jelly

### RICKSHAW COLDBREW – \\$5.00 per bottle (No GST)

Minimum 30 pax
Using traditional Hainanese coffee, slow-steeped to extract only the good stuff

*As featured on RubbishEatRubbishGrow:
[https://rubbisheatrubbishgrow.com/2020/07/23/195-pearl-hill-cafe-chinatown/](https://rubbisheatrubbishgrow.com/2020/07/23/195-pearl-hill-cafe-chinatown/)*

---

## FAQs

* For all orders, we require *1–2 weeks' notice*.
* For *coffee and tea orders only*, contact us directly. We cater to orders 1–2 days in advance.
* Drinks are served *with milk and sugar by default, but **black/kosong* is available upon request.
* We offer *a wider variety of kueh* beyond the listed ones—these are the most popular.
* All sandwiches and kueh are *individually wrapped* for easy handling during standing/networking events.
* We can provide *e-invoicing* and *receipts* for claim submissions.
* Prices apply only to *events/orders at MFA, 1 Sherwood Road*.
* For enquiries, contact *Ben at 88299097*."""

CATERING_SUMMARY = "Comprehensive catering services including bento sets, teabreak packages, and specialty items for corporate events and gatherings."

def populate_content_categories():
    """Populate content categories for both bots."""
    
    # Content categories to add for Rickshaw Coffee bot
    rickshaw_categories = [
        ContentCategory(
            bot_id=UUID(RICKSHAW_COFFEE_BOT_ID),
            category_type=ContentCategoryType.DAILY_FOOD_MENU,
            title="Daily Food Menu",
            content=DAILY_FOOD_MENU_CONTENT,
            summary=DAILY_FOOD_MENU_SUMMARY
        ),
        ContentCategory(
            bot_id=UUID(RICKSHAW_COFFEE_BOT_ID),
            category_type=ContentCategoryType.PRODUCTS,
            title="Rickshaw Coffee Products",
            content=PRODUCTS_CONTENT,
            summary=PRODUCTS_SUMMARY
        ),
        ContentCategory(
            bot_id=UUID(RICKSHAW_COFFEE_BOT_ID),
            category_type=ContentCategoryType.CATERING,
            title="Catering Services",
            content=CATERING_CONTENT,
            summary=CATERING_SUMMARY
        )
    ]
    
    try:
        print("Populating content categories...")
        
        for category in rickshaw_categories:
            print(f"Adding {category.category_type.value} for Rickshaw Coffee bot...")
            supabase_client.insert_content_category(category)
            print(f"✓ Added {category.category_type.value}")
        
        print("\n✅ Content categories populated successfully!")
        
    except Exception as e:
        print(f"❌ Error populating content categories: {e}")
        raise

if __name__ == "__main__":
    populate_content_categories()
